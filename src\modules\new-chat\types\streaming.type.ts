import { IChatError } from "./handlers.type";

export interface IStreamingChunkData {
	content: string;
	confidence: number;
	done: boolean;
}

export interface IStreamingOptions {
	onChunk?: (content: string) => void;
	onChunkData?: (chunkData: IStreamingChunkData) => void;
	onComplete?: (fullMessage: string) => void;
	onError?: (error: IChatError) => void;
	signal?: AbortSignal;
}

export interface IChatStreamRequest {
	message: string;
	sessionId?: string;
}

export interface IChatStreamResponse {
	content: string;
	chunkData?: IStreamingChunkData;
	done?: boolean;
}
