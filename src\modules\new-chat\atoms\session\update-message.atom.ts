import { atom } from "jotai";
import { IChatMessage } from "../../types/messages.type";
import { chatStreamingSessionInfo } from "./info.atom";

export const updateMessageAtom = atom(null, (get, set, messageId: string, updates: Partial<IChatMessage>) => {
	const session = get(chatStreamingSessionInfo);
	if (!session) return;
	set(chatStreamingSessionInfo, {
		...session,
		messages: session.messages.map(msg => (msg.id === messageId ? { ...msg, ...updates } : msg)),
		updatedAt: new Date(),
	});
});
