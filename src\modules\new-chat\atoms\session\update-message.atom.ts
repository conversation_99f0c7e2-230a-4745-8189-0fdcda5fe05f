import { atom } from "jotai";
import { IChatMessage } from "../../types/messages.type";
import { chatStreamingSessionInfo } from "./info.atom";

export const updateMessageAtom = atom(null, (get, set, messageId: string, updates: Partial<IChatMessage>) => {
	const session = get(chatStreamingSessionInfo);
	if (!session) {
		console.log("No session found for message update");
		return;
	}

	console.log("Updating message:", messageId, "with content:", updates.content?.substring(0, 50) + "...");

	const updatedMessages = session.messages.map(msg => {
		if (msg.id === messageId) {
			const updatedMsg = { ...msg, ...updates };
			console.log("Message updated:", updatedMsg.content?.substring(0, 50) + "...");
			return updatedMsg;
		}
		return msg;
	});

	set(chatStreamingSessionInfo, {
		...session,
		messages: updatedMessages,
		updatedAt: new Date(),
	});
});
