import { IStreamingChunkData } from "../types/streaming.type";

interface StreamResponseData {
	content?: string;
	delta?: {
		content?: string;
	};
	choices?: Array<{
		delta?: {
			content?: string;
		};
	}>;
	text?: string;
	confidence?: number;
	done?: boolean;
}

export function processBuffer(buffer: string) {
	const lines = buffer.split("\n");
	const remainingBuffer = lines.pop() || "";
	const processedLines = lines.filter(line => line.trim());
	return { processedLines, remainingBuffer };
}

export function processStreamLine(line: string): {
	content: string;
	shouldComplete: boolean;
	chunkData?: IStreamingChunkData;
} {
	const trimmed = line.trim();
	if (!trimmed) return { content: "", shouldComplete: false };
	if (trimmed.startsWith("data: ")) {
		const data = trimmed.slice(6).trim();
		if (data === "[DONE]") return { content: "", shouldComplete: true };
		return parseStreamData(data);
	}
	return parseStreamData(trimmed);
}

export function parseStreamData(data: string) {
	try {
		const parsed: StreamResponseData = JSON.parse(data);
		return {
			content: extractContent(parsed),
			shouldComplete: parsed.done === true,
			chunkData: createChunkData(parsed),
		};
	} catch {
		return { content: data, shouldComplete: false };
	}
}

function extractContent(parsed: StreamResponseData) {
	return parsed.content || parsed.delta?.content || parsed.choices?.[0]?.delta?.content || parsed.text || "";
}

function createChunkData(parsed: StreamResponseData): IStreamingChunkData | undefined {
	if (typeof parsed.content === "string" && typeof parsed.confidence === "number" && typeof parsed.done === "boolean") {
		return { content: parsed.content, confidence: parsed.confidence, done: parsed.done };
	}
}
