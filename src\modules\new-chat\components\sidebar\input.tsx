import { SendHorizonal } from "lucide-react";
import { useState } from "react";
import { Button } from "../../../../shared/components/shadcn/button";
import { Textarea } from "../../../../shared/components/shadcn/textarea";
import { cn } from "../../../../shared/lib/shadcn/utils";
import { useChatInput } from "../../hooks/handle-input.hook";

interface IChatInputProps {
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
	placeholder?: string;
}

export const ChatInput = ({ onSend, onStop, disabled, placeholder }: IChatInputProps) => {
	const [inputValue, setInputValue] = useState("");

	const { canSend, canStop, handleKeyDown, handleSend } = useChatInput({
		value: inputValue,
		onChange: setInputValue,
		onSend,
		onStop,
		disabled,
		onMessageSent: () => setInputValue(""),
	});

	return (
		<div
			className={cn(
				`relative flex items-end gap-3 overflow-hidden border-t border-slate-200 bg-gradient-to-r from-slate-50 via-white to-slate-50 px-6 py-4 shadow-sm backdrop-blur-md transition-all duration-300 ease-in-out dark:border-slate-700 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900`,
			)}
		>
			<div className="from-primary/5 to-primary/5 pointer-events-none absolute inset-0 bg-gradient-to-r via-transparent" />
			<div className="via-primary/30 absolute top-0 right-0 left-0 h-px bg-gradient-to-r from-transparent to-transparent" />

			<div className="relative z-10 flex-1">
				<Textarea
					value={inputValue}
					onChange={e => setInputValue(e.target.value)}
					onKeyDown={handleKeyDown}
					placeholder={placeholder}
					disabled={disabled}
					className="focus:border-primary dark:focus:border-primary focus:ring-primary/20 max-h-32 min-h-[48px] resize-none rounded-xl border-slate-200 bg-white px-4 py-3 text-slate-700 shadow-sm transition-all duration-200 placeholder:text-slate-400 hover:shadow-md focus:shadow-md focus:ring-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-600 dark:bg-slate-800 dark:text-slate-200 dark:placeholder:text-slate-500"
					rows={1}
				/>
			</div>

			<Button
				onClick={() => onSend(inputValue)}
				disabled={!canSend}
				size="sm"
				className="bg-primary hover:bg-primary/90 group relative z-10 h-12 w-12 shrink-0 rounded-xl p-0 shadow-sm transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed disabled:bg-slate-300 dark:disabled:bg-slate-600"
				aria-label="Enviar mensagem"
			>
				<SendHorizonal className="h-6 w-6 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:scale-110" />
			</Button>
			{/* {canStop ? (
				<Button
					onClick={onStop}
					size="sm"
					variant="outline"
					className="group relative z-10 h-12 w-12 shrink-0 rounded-xl border-red-200 bg-white p-0 text-red-600 shadow-sm transition-all duration-200 hover:border-red-300 hover:bg-red-50 hover:shadow-md dark:border-red-800 dark:bg-slate-800 dark:text-red-400 dark:hover:border-red-700 dark:hover:bg-red-950/30"
					aria-label="Parar geração"
				>
					<Square className="h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
				</Button>
			) : (
				<Button
					onClick={onSend}
					disabled={!canSend}
					size="sm"
					className="bg-primary hover:bg-primary/90 group relative z-10 h-12 w-12 shrink-0 rounded-xl p-0 shadow-sm transition-all duration-200 hover:shadow-md disabled:cursor-not-allowed disabled:bg-slate-300 dark:disabled:bg-slate-600"
					aria-label="Enviar mensagem"
				>
					<SendHorizonal className="h-6 w-6 transition-transform duration-200 group-hover:translate-x-0.5 group-hover:scale-110" />
				</Button>
			)} */}
		</div>
	);
};
