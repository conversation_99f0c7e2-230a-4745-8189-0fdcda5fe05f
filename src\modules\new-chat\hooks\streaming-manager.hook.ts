"use client";

import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { chatErrorAtom } from "../atoms/handlers/error.atom";
import { addMessageAtom } from "../atoms/session/add-message.atom";
import { sessionId<PERSON>tom } from "../atoms/session/info.atom";
import { updateMessageAtom } from "../atoms/session/update-message.atom";
import { sendStreamingMessage } from "../lib/streaming-send-message.lib";

export const useStreamingManager = () => {
	const currentSessionId = useAtomValue(sessionIdAtom);
	const addMessage = useSetAtom(addMessageAtom);
	const updateMessage = useSetAtom(updateMessageAtom);
	const setError = useSetAtom(chatErrorAtom);

	const sendMessage = useCallback(
		async (content: string) => {
			if (!content.trim()) return;
			const sessionId = currentSessionId;
			if (!sessionId) throw new Error("O id da sessão não está disponível");
			try {
				await sendStreamingMessage({
					sessionId,
					content,
					addMessage,
					onUpdate: updateMessage,
					setError,
				});
			} catch (error) {
				console.error("Error sending message:", error);
				throw error;
			}
		},
		[addMessage, currentSessionId, setError, updateMessage],
	);

	return {
		sendMessage,
	};
};
