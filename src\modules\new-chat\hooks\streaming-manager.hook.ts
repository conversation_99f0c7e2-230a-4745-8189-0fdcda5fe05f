"use client";

import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { useCallback } from "react";
import { chatError<PERSON>tom } from "../atoms/handlers/error.atom";
import { addMessageAtom } from "../atoms/session/add-message.atom";
import { chatStreamingMessagesAtom, sessionId<PERSON>tom } from "../atoms/session/info.atom";
import { updateMessageAtom } from "../atoms/session/update-message.atom";
import { sendStreamingMessage } from "../lib/streaming-send-message.lib";

export const useStreamingManager = () => {
	const currentSessionId = useAtomValue(sessionIdAtom);
	const messages = useAtomValue(chatStreamingMessagesAtom);
	const addMessage = useSetAtom(addMessageAtom);
	const updateMessage = useSetAtom(updateMessageAtom);
	const setError = useSetAtom(chatErrorAtom);

	const getCurrentMessage = useCallback(
		(messageId: string) => {
			return messages.find(msg => msg.id === messageId);
		},
		[messages],
	);

	const sendMessage = useCallback(
		async (content: string) => {
			if (!content.trim()) return;
			const sessionId = currentSessionId;
			if (!sessionId) throw new Error("O id da sessão não está disponível");
			try {
				await sendStreamingMessage({
					sessionId,
					content,
					addMessage,
					onUpdate: updateMessage,
					setError,
					getCurrentMessage,
				});
			} catch (error) {
				console.error("Error sending message:", error);
				throw error;
			}
		},
		[addMessage, currentSessionId, setError, updateMessage, getCurrentMessage],
	);

	return {
		sendMessage,
	};
};
