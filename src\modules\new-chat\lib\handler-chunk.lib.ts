import { IChatMessage } from "../types/messages.type";

interface IStreamingChunkHandler {
	chunk: string;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	accumulatedContent: { current: string };
}

export function handleStreamingChunk({ chunk, updateMessage, assistantMessageId, accumulatedContent }: IStreamingChunkHandler) {
	console.log("Received chunk:", chunk);

	// Accumulate content in the ref
	accumulatedContent.current += chunk;

	updateMessage(assistantMessageId, {
		content: accumulatedContent.current,
	});
}
