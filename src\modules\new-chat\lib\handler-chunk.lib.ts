import { IChatMessage } from "../types/messages.type";

interface IStreamingChunkHandler {
	chunk: string;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	accumulatedContent: { current: string };
}

export function handleStreamingChunk({ chunk, updateMessage, assistantMessageId, accumulatedContent }: IStreamingChunkHandler) {
	console.log("Received chunk:", chunk);
	console.log("Current accumulated content:", accumulatedContent.current);

	// Accumulate content in the ref
	accumulatedContent.current += chunk;

	console.log("New accumulated content:", accumulatedContent.current);

	// Update the message with the accumulated content immediately
	updateMessage(assistantMessageId, {
		content: accumulatedContent.current,
	});
}
