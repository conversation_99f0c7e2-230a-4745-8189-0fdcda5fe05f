import { IChatMessage } from "../types/messages.type";

interface IStreamingChunkHandler {
	chunk: string;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
	getCurrentMessage: (id: string) => IChatMessage | undefined;
}

export function handleStreamingChunk({ chunk, updateMessage, assistantMessageId, getCurrentMessage }: IStreamingChunkHandler) {
	console.log("Received chunk:", chunk);

	// Get current message content and append the new chunk
	const currentMessage = getCurrentMessage(assistantMessageId);
	const currentContent = currentMessage?.content || "";

	updateMessage(assistantMessageId, {
		content: currentContent + chunk,
	});
}
