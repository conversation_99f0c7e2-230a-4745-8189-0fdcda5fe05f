import { IChatMessage } from "../types/messages.type";

interface IStreamingChunkHandler {
	chunk: string;
	updateMessage: (id: string, updates: Partial<IChatMessage>) => void;
	assistantMessageId: string;
}

export function handleStreamingChunk({ chunk, updateMessage, assistantMessageId }: IStreamingChunkHandler) {
	console.log("Received chunk:", chunk);

	updateMessage(assistantMessageId, {
		content: chunk,
	});
}
