import { chatStreamService } from "../services/streaming/streaming.service";
import { IChatError } from "../types/handlers.type";
import { IChatMessage } from "../types/messages.type";
import { IChatStreamRequest } from "../types/streaming.type";
import { handleStreamingChunk } from "./handler-chunk.lib";

interface IStreamingMessageProps {
	sessionId: string;
	content: string;
	addMessage: (message: Omit<IChatMessage, "id" | "timestamp">) => string;
	onUpdate: (messageId: string, updates: Partial<IChatMessage>) => void;
	setError: (error: string | null) => void;
	getCurrentMessage: (messageId: string) => IChatMessage | undefined;
}

export const sendStreamingMessage = async ({ sessionId, content, addMessage, onUpdate, setError, getCurrentMessage }: IStreamingMessageProps) => {
	setError(null);
	addMessage({
		content: content.trim(),
		role: "user",
	});

	const assistantMessage = await addMessage({
		content: "",
		role: "assistant",
	});

	if (!assistantMessage) throw new Error("Ocorreu um erro ao criar a mensagem do assistente. [CLIENT-ID-CREATOR]");

	const request: IChatStreamRequest = {
		sessionId,
		message: content.trim(),
	};

	await chatStreamService.streamChat(request, {
		onChunk: (chunk: string) => handleStreamingChunk({ chunk, updateMessage: onUpdate, assistantMessageId: assistantMessage, getCurrentMessage }),
		onError: (error: IChatError) => {
			setError(error.message || "Erro ao processar resposta");
			onUpdate(assistantMessage, {
				content: error.message || "Erro ao processar resposta",
				isError: true,
			});
		},
	});
};
