import { useCallback } from "react";

interface ChatInputState {
	value: string;
	onChange: (value: string) => void;
	onSend: (content: string) => Promise<void>;
	onStop?: () => void;
	disabled?: boolean;
}

export const useChatInput = ({ value, onSend, onStop, disabled }: ChatInputState) => {
	const canSend = !disabled && value.trim().length > 0;
	const canStop = onStop;

	const handleKeyDown = useCallback(
		(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
			if (e.key === "Enter" && !e.shiftKey) {
				e.preventDefault();
				if (canSend) onSend(value);
			}
		},
		[canSend, onSend, value],
	);

	return {
		canSend,
		canStop,
		handleKeyDown,
	};
};
