import { Bo<PERSON>, User } from "lucide-react";
import { memo } from "react";
import { cn } from "../../../../shared/lib/shadcn/utils";

import { IChatMessage } from "../../types/messages.type";
import { TypingIndicator } from "./typing-indicador";

interface IChatMessageProps {
	message: IChatMessage;
}

const getMessageStyles = (isUser: boolean, isError?: boolean) => {
	if (isError) return "border-red-200/60 bg-gradient-to-br from-red-50/80 to-red-100/80 text-red-800 shadow-red-100/60 ";
	if (isUser) return "bg-gradient-to-br from-primary to-blue-600 text-white shadow-blue-500/25 border border-blue-400/30";
	return "bg-gradient-to-br from-slate-50/90 to-white/90 text-slate-700 shadow-slate-200/40 border border-slate-200/60";
};

const getAvatarStyles = (isUser: boolean) => {
	if (isUser) return "bg-gradient-to-br from-slate-100 to-slate-200 border-slate-200/80 text-slate-600";
	return "bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200/60 text-blue-600 ";
};

const formatTimestamp = (timestamp: Date) => {
	return new Date(timestamp).toLocaleTimeString([], {
		hour: "2-digit",
		minute: "2-digit",
	});
};

export const ChatMessage = memo<IChatMessageProps>(({ message }) => {
	const isUser = message.role === "user";
	const isAssistant = message.role === "assistant";
	const showTypingIndicator = isAssistant && message.id && !message.content.trim();
	if (showTypingIndicator) return <TypingIndicator />;

	return (
		<div
			className={cn(
				"group flex gap-2.5 px-3 py-2 transition-all duration-200 hover:bg-slate-50/50 dark:hover:bg-slate-800/30",
				isUser ? "justify-end" : "justify-start",
			)}
		>
			{isAssistant && (
				<div
					className={cn(
						"flex h-7 w-7 shrink-0 items-center justify-center rounded-full border shadow-sm transition-all duration-200 group-hover:scale-105 group-hover:shadow-md",
						getAvatarStyles(false),
					)}
				>
					<Bot className="h-3.5 w-3.5" />
				</div>
			)}
			<div className="flex max-w-[75%] flex-col gap-1">
				<div
					className={cn(
						"rounded-main px-3 py-2.5 text-sm shadow-sm backdrop-blur-sm transition-all duration-200 group-hover:shadow-md",
						getMessageStyles(isUser, message.isError),
					)}
				>
					<div className="leading-relaxed break-words whitespace-pre-wrap">
						{/* {isAssistant && isStreaming ? <TypewriterText text={message.content} isStreaming={isStreaming} speed={20} /> : message.content} */}
					</div>
				</div>
				{/* {!isStreaming && message.timestamp && (
					<div
						className={cn(
							"px-2 text-xs font-medium opacity-50 transition-opacity duration-200 group-hover:opacity-70",
							isUser ? "text-right text-slate-600 dark:text-slate-400" : "text-left text-slate-500 dark:text-slate-400",
						)}
					>
						{formatTimestamp(new Date(message.timestamp))}
					</div>
				)} */}
				{message.timestamp && (
					<div
						className={cn(
							"px-2 text-xs font-medium opacity-50 transition-opacity duration-200 group-hover:opacity-70",
							isUser ? "text-right text-slate-600 dark:text-slate-400" : "text-left text-slate-500 dark:text-slate-400",
						)}
					>
						{formatTimestamp(new Date(message.timestamp))}
					</div>
				)}
			</div>

			{/* Avatar do Usuário */}
			{isUser && (
				<div
					className={cn(
						"flex h-7 w-7 shrink-0 items-center justify-center rounded-full border shadow-sm transition-all duration-200 group-hover:scale-105 group-hover:shadow-md",
						getAvatarStyles(true),
					)}
				>
					<User className="h-3.5 w-3.5" />
				</div>
			)}
		</div>
	);
});
ChatMessage.displayName = "ChatMessage";
