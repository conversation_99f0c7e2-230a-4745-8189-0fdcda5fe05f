"use client";
import { atom } from "jotai";
import { IChatMessage } from "../../types/messages.type";
import { createNewSessionAtom } from "./create.atom";
import { chatStreamingSessionInfo } from "./info.atom";

export const addMessageAtom = atom(null, (get, set, message: Omit<IChatMessage, "id" | "timestamp">) => {
	let session = get(chatStreamingSessionInfo);
	if (!session) {
		set(createNewSessionAtom);
		session = get(chatStreamingSessionInfo);
		if (!session) throw new Error("Erro ao criar nova sessão");
	}
	const newMessage: IChatMessage = {
		...message,
		id: crypto.randomUUID(),
		timestamp: Date.now(),
	};

	console.log("New message added:", newMessage);

	set(chatStreamingSessionInfo, {
		...session,
		messages: [...session.messages, newMessage],
	});
	return newMessage.id;
});
