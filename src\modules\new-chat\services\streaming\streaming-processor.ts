import { processStreamLine } from "../../lib/streaming-service.lib";
import { IStreamingOptions } from "../../types/streaming.type";

export class StreamProcessor {
	async process(body: ReadableStream<Uint8Array>, options: IStreamingOptions) {
		const reader = body.getReader();
		const decoder = new TextDecoder();
		let fullMessage = "";
		let buffer = "";

		try {
			while (true) {
				const { done, value } = await reader.read();
				if (done) break;

				buffer += decoder.decode(value, { stream: true });
				const lines = buffer.split("\n");
				buffer = lines.pop() || "";

				for (const line of lines.filter(l => l.trim())) {
					const result = processStreamLine(line);
					console.log("Processed line result:", result);
					if (result.shouldComplete) {
						console.log("Stream completed, full message:", fullMessage);
						options.onComplete?.(fullMessage);
						return;
					}
					if (result.content) {
						fullMessage += result.content;
						console.log("Calling onChunk with:", result.content);
						options.onChunk?.(result.content);
						if (result.chunkData) options.onChunkData?.(result.chunkData);
					}
				}
			}
			options.onComplete?.(fullMessage);
		} finally {
			reader.releaseLock();
		}
	}
}
